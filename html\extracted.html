<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Hustle Hub</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/gpace-logo-white.png">

    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../css/sideDrawer.css" rel="stylesheet">

    <!-- Firebase SDKs -->
    
    
    
    

<link href="../css/extracted.css" rel="stylesheet">
    <!-- Add this right after the Firebase SDKs script -->
    

    <!-- Add this in the head section -->
    

    <!-- Modify the navigation links to use the page manager -->
    
</head>

<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img
                src="../assets/images/gpace-logo-white.png"
                alt="GPAce Logo"
                style="height: 60px; margin-right: 0px;"
            >
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html" class="active">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="flashcards.html">Flashcards</a>
        </div>
    </nav>

    <div class="main-container">
        <div class="sidebar" id="projectsSidebar">
            <!-- Projects will be dynamically added here -->
        </div>

        <!-- Add sidebar toggle button -->
        <button id="sidebarToggle" class="btn btn-sm sidebar-toggle">
            <i class="bi bi-chevron-left"></i>
        </button>

        <!-- Main Content -->
        <div class="main-content">
            <div id="taskContainer" class="fade-in">
                <!-- Tasks will be dynamically added here -->
            </div>
        </div>

        <!-- Add a hidden overlay for mobile sidebar -->
        <div id="sidebarOverlay" class="sidebar-overlay" style="display: none;"></div>
    </div>

    <!-- Add Weightage Modal -->
    <div id="weightageModal" class="weightage-modal">
        <div class="weightage-modal-content">
            <h5 class="mb-3">Set Subsection Weightages</h5>
            <p class="text-secondary mb-3" style="font-size: 0.9rem;">Enter min-max ranges for each subsection. The average will be calculated automatically.</p>
            <div id="weightageInputs" class="mb-3">
                <!-- Inputs will be dynamically added here -->
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="text-secondary">Total Average:</span>
                <span id="totalAverage" class="weightage-avg">0%</span>
            </div>
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-secondary" onclick="closeWeightageModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveWeightages()">Save</button>
            </div>
        </div>
    </div>

    <!-- Edit Task Modal -->
    <div id="editTaskModal" class="edit-task-modal">
        <div class="edit-task-content">
            <h5>Edit Task</h5>
            <input type="hidden" id="editTaskIndex">
            <input type="hidden" id="editTaskSection">
            <input type="text" id="editTaskTitle" class="form-control mb-2" placeholder="Task Title">
            <textarea id="editTaskDescription" class="form-control mb-2" placeholder="Description (optional)"></textarea>
            <input type="date" id="editTaskDueDate" class="form-control mb-2">
            <select id="editTaskPriority" class="form-control mb-3">
                <option value="low">Low Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="high">High Priority</option>
            </select>
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-secondary" onclick="closeEditTaskModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveEditedTask()">Save Changes</button>
            </div>
        </div>
    </div>

    <div id="addTaskForm" class="task-form">
        <div class="task-input-toggle mb-3">
            <button class="btn btn-sm btn-outline-primary" onclick="toggleInputMode('single')">Single Task</button>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleInputMode('bulk')">Bulk Add</button>
        </div>

        <div id="singleTaskInput">
            <input type="text" id="taskTitle" class="form-control mb-2" placeholder="Task Title">
            <textarea id="taskDescription" class="form-control mb-2" placeholder="Description (optional)"></textarea>
            <input type="date" id="taskDueDate" class="form-control mb-2">
            <select id="taskPriority" class="form-control mb-3">
                <option value="low">Low Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="high">High Priority</option>
            </select>
        </div>

        <div id="bulkTaskInput" style="display: none;">
            <div class="input-format-example">
                Format: Title | Due Date (YYYY-MM-DD) | Priority (low/medium/high) | Description<br>
                Example: Complete math homework | 2024-01-20 | high | Chapter 5 exercises
            </div>
            <textarea id="bulkTasks" class="bulk-input" placeholder="Enter one task per line..."></textarea>
        </div>

        <button onclick="addTask()" class="btn btn-primary">Add Task(s)</button>
        <button onclick="hideAddTaskForm()" class="btn btn-secondary">Cancel</button>
    </div>

    <!-- History Modal -->
    <div id="historyModal" class="history-modal">
        <div class="history-content">
            <div class="history-header">
                <h5 class="mb-0">Completed Tasks History</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="closeHistoryModal()">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div id="historyList">
                <!-- Completed tasks will be displayed here -->
            </div>
        </div>
    </div>
    
    
    
    
    
    
    
    
    
    
    
</body>

</html>
    
    
    
    
    
    
    
    
    
    
    
</body>

</html>
    <!-- Scripts -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithPopup, GoogleAuthProvider } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { saveTasksToFirestore, loadTasksFromFirestore, saveCompletedTaskToFirestore, saveWeightagesToFirestore, loadWeightagesFromFirestore } from './js/firestore.js';
        import crossTabSync from './js/cross-tab-sync.js';
        import userGuidance from './js/userGuidance.js';
        import googleDriveAPI from './js/googleDriveApi.js';
        import taskAttachments from './js/taskAttachments.js';
        import dataSyncManager from './js/data-sync-manager.js';

        // Initialize Firebase with config
        const app = initializeApp({
            apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
            authDomain: "mzm-gpace.firebaseapp.com",
            projectId: "mzm-gpace",
            storageBucket: "mzm-gpace.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:3aa05a6e133e2066c45187"
        });

        // Set up auth
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();

        // Make auth and functions available globally
        window.auth = auth;
        window.signInWithGoogle = async () => {
            try {
                await signInWithPopup(auth, provider);
            } catch (error) {
                console.error('Error signing in with Google:', error);
            }
        };
        window.signOutUser = async () => {
            try {
                await auth.signOut();
            } catch (error) {
                console.error('Error signing out:', error);
            }
        };
        window.saveTasksToFirestore = saveTasksToFirestore;
        window.loadTasksFromFirestore = loadTasksFromFirestore;
        window.saveCompletedTaskToFirestore = saveCompletedTaskToFirestore;
        window.saveWeightagesToFirestore = saveWeightagesToFirestore;
        window.loadWeightagesFromFirestore = loadWeightagesFromFirestore;

        // Initialize Google Drive API without await
        googleDriveAPI.initialize()
            .then(() => {
                console.log('Google Drive API initialized successfully');
            })
            .catch(error => {
                console.error('Error initializing Google Drive API:', error);
            });

        // Make task attachments globally available
        window.taskAttachments = taskAttachments;

        // Add cross-tab synchronization for page reload
        crossTabSync.onUserAction('task-update', (data) => {
            console.log(' Task update received for project:', data.projectId);
            console.log(' Reloading page due to task update');
            location.reload();
        });
    </script>
    <script type="module" src="../js/sideDrawer.js"></script>
    <script type="module" src="../js/cross-tab-sync.js"></script>
    <script type="module">
        import { syncProjectToSubjectWeightages } from './js/weightage-connector.js';
        window.syncProjectToSubjectWeightages = syncProjectToSubjectWeightages;

        // Import flashcard task integration functions
        import { findSubDeckForTask, connectTaskToFlashcardDeck } from './js/flashcardTaskIntegration.js';
        window.findSubDeckForTask = findSubDeckForTask;
        window.connectTaskToFlashcardDeck = connectTaskToFlashcardDeck;
    </script>
    <script type="module">
        // State manager for sharing data between pages
        window.appState = {
            initialized: false,
            cache: {},
            projectCache: {
                tasks: {},
                get weightages() {
                    return window.appState?.cache?.weightages || null;
                },
                lastFetch: {},
                isFetching: {},
                cacheTTL: 30000,

                // Check if cache is still valid
                isValid: function(projectId) {
                    const now = Date.now();
                    return this.tasks[projectId] &&
                           this.lastFetch[projectId] &&
                           (now - this.lastFetch[projectId] < this.cacheTTL);
                },

                // Store project tasks in cache
                setTasks: function(projectId, tasks) {
                    this.tasks[projectId] = tasks;
                    this.lastFetch[projectId] = Date.now();
                    this.isFetching[projectId] = false;

                    // Also store in app state for cross-page persistence
                    window.appState.cache[`tasks-${projectId}`] = tasks;
                },

                // Clear cache for a specific project
                clearProject: function(projectId) {
                    delete this.tasks[projectId];
                    delete this.lastFetch[projectId];
                    delete window.appState.cache[`tasks-${projectId}`];
                },

                // Force refresh data for a project
                refreshProject: async function(projectId) {
                    if (this.isFetching[projectId]) return;

                    this.isFetching[projectId] = true;

                    try {
                        // Try to get from app state first
                        if (window.appState.cache[`tasks-${projectId}`]) {
                            this.setTasks(projectId, window.appState.cache[`tasks-${projectId}`]);
                            return this.tasks[projectId];
                        }

                        const tasks = await loadTasksFromFirestore(projectId) || [];
                        this.setTasks(projectId, tasks);
                        return tasks;
                    } catch (error) {
                        console.error('Error refreshing project data:', error);
                        this.isFetching[projectId] = false;
                        throw error;
                    }
                }
            },

            async initialize() {
                if (this.initialized) return;

                // Initialize Firebase only once
                const app = initializeApp({
                    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
                    authDomain: "mzm-gpace.firebaseapp.com",
                    projectId: "mzm-gpace",
                    storageBucket: "mzm-gpace.firebasestorage.app",
                    messagingSenderId: "************",
                    appId: "1:************:web:3aa05a6e133e2066c45187"
                });

                const auth = getAuth(app);
                const provider = new GoogleAuthProvider();

                // Store in global state
                this.auth = auth;
                this.provider = provider;
                this.db = getFirestore(app);

                // Initialize APIs
                await Promise.all([
                    googleDriveAPI.initialize(),
                    this.preloadCommonData()
                ]);

                this.initialized = true;
                console.log('App state initialized');
            },

            async preloadCommonData() {
                if (this.auth?.currentUser) {
                    // Preload subjects
                    const subjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
                    this.cache.subjects = subjects;

                    // Preload weightages
                    try {
                        this.cache.weightages = await loadWeightagesFromFirestore();
                    } catch (error) {
                        console.warn('Could not preload weightages:', error);
                    }
                }
            }
        };

        // Initialize app state when the module loads
        window.appState.initialize().catch(console.error);

        // Add page transition manager
        window.pageManager = {
            currentPage: window.location.pathname,
            pageCache: new Map(),

            preloadPage(url) {
                if (this.pageCache.has(url)) return;

                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = url;
                document.head.appendChild(link);
                this.pageCache.set(url, true);
            },

            navigateTo(url) {
                // Save current scroll position
                const scrollPos = {
                    x: window.scrollX,
                    y: window.scrollY
                };
                sessionStorage.setItem('scrollPos-' + this.currentPage, JSON.stringify(scrollPos));

                // Navigate to new page
                window.location.href = url;
            }
        };

        // Preload linked pages
        document.querySelectorAll('a').forEach(link => {
            const url = link.getAttribute('href');
            if (url && !url.startsWith('#') && !url.startsWith('javascript:')) {
                window.pageManager.preloadPage(url);
            }
        });

        // Restore scroll position if coming back to a page
        window.addEventListener('load', () => {
            const scrollPos = sessionStorage.getItem('scrollPos-' + window.location.pathname);
            if (scrollPos) {
                const { x, y } = JSON.parse(scrollPos);
                window.scrollTo(x, y);
            }
        });
    </script>
    <script>
        // Preload critical resources
        const resourcesToPreload = [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css',
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js'
        ];

        resourcesToPreload.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = url.endsWith('.css') ? 'style' : 'script';
            link.href = url;
            document.head.appendChild(link);
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('.nav-links a').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    window.pageManager.navigateTo(link.href);
                });
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check for workspace panel from grind.html
        document.addEventListener('DOMContentLoaded', function() {
            // Listen for workspace panel open/close events
            window.addEventListener('message', function(event) {
                if (event.data === 'workspace-opened') {
                    document.body.classList.add('workspace-panel-open');
                } else if (event.data === 'workspace-closed') {
                    document.body.classList.remove('workspace-panel-open');
                }
            });

            // Handle sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('projectsSidebar');
            const mainContent = document.querySelector('.main-content');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (sidebarToggle && sidebar) {
                // Toggle sidebar function
                function toggleSidebar() {
                    sidebar.classList.toggle('closing');
                    mainContent.classList.toggle('full-width');
                    sidebarToggle.classList.toggle('collapsed');

                    // For mobile view
                    sidebar.classList.toggle('active');
                    sidebarToggle.classList.toggle('active');

                    // Toggle overlay on mobile
                    if (window.innerWidth <= 992) {
                        if (sidebar.classList.contains('active')) {
                            sidebarOverlay.style.display = 'block';
                        } else {
                            sidebarOverlay.style.display = 'none';
                        }
                    }
                }

                // Sidebar toggle click event
                sidebarToggle.addEventListener('click', toggleSidebar);

                // Overlay click closes sidebar on mobile
                if (sidebarOverlay) {
                    sidebarOverlay.addEventListener('click', function() {
                        if (sidebar.classList.contains('active')) {
                            toggleSidebar();
                        }
                    });
                }

                // Auto-collapse sidebar on mobile
                if (window.innerWidth <= 992) {
                    sidebar.classList.add('closing');
                    mainContent.classList.add('full-width');
                    sidebarToggle.classList.add('collapsed');
                }

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth <= 992) {
                        if (!sidebar.classList.contains('closing')) {
                            // Show overlay when sidebar is open on mobile
                            sidebarOverlay.style.display = 'block';
                        }
                    } else {
                        // Hide overlay on desktop
                        sidebarOverlay.style.display = 'none';
                    }
                });
            }

            // Fix for userGuidance.js help button positioning
            const helpButton = document.querySelector('button[style*="position: fixed"][style*="border-radius: 50%"]');
            if (helpButton) {
                helpButton.style.top = '70px';
                helpButton.style.left = 'auto';
                helpButton.style.right = '20px';
                helpButton.style.zIndex = '150';
            }
        });

        const SUBSECTIONS = ['Revision', 'Assignment', 'Quizzes', 'Mid Term / OHT', 'Finals'];
        let currentProject = null;
        let currentSection = null;
        let currentInputMode = 'single';

        // Add a cache object to store project data
        const projectCache = {
            tasks: {},        // Store tasks by project ID
            weightages: null, // Store all weightages
            lastFetch: {},    // Track last fetch timestamp by project ID
            isFetching: {},   // Track ongoing fetch requests
            cacheTTL: 30000,  // Cache time-to-live in ms (30 seconds)

            // Check if cache is still valid
            isValid: function(projectId) {
                const now = Date.now();
                return this.tasks[projectId] &&
                       this.lastFetch[projectId] &&
                       (now - this.lastFetch[projectId] < this.cacheTTL);
            },

            // Store project tasks in cache
            setTasks: function(projectId, tasks) {
                this.tasks[projectId] = tasks;
                this.lastFetch[projectId] = Date.now();
                this.isFetching[projectId] = false;
            },

            // Store weightages in cache
            setWeightages: function(weightages) {
                this.weightages = weightages;
            },

            // Clear cache for a specific project
            clearProject: function(projectId) {
                delete this.tasks[projectId];
                delete this.lastFetch[projectId];
            },

            // Force refresh data for a project
            refreshProject: async function(projectId) {
                // Skip if already fetching
                if (this.isFetching[projectId]) return;

                this.isFetching[projectId] = true;

                try {
                    const tasks = await loadTasksFromFirestore(projectId) || [];
                    this.setTasks(projectId, tasks);

                    if (!this.weightages) {
                        this.setWeightages(await loadWeightagesFromFirestore() || {});
                    }

                    return tasks;
                } catch (error) {
                    console.error('Error refreshing project data:', error);
                    this.isFetching[projectId] = false;
                    throw error;
                }
            }
        };

        // Create audio element for custom sound
        const popSound = new Audio('sounds/pop.mp3');
        popSound.volume = 0.5; // Adjust volume (0.0 to 1.0)
        popSound.preload = 'auto'; // Preload the sound

        // Function to play custom sound
        async function playPopSound() {
            try {
                popSound.currentTime = 0; // Reset sound to start
                await popSound.play();
            } catch (error) {
                console.log('Sound playback failed:', error);
            }
        }
           //This depends on the input academic details that are arriving from the local storage of academic-details.html, so this
           //function is going to be called from there no other change is needed
        async function loadProjects() {
            const isAuthenticated = window.auth && window.auth.currentUser;
            if (!isAuthenticated) {
                console.log('User not signed in when loading projects');

                // Only show welcome dialog on explicit user action, not on page load
                const sidebar = document.getElementById('projectsSidebar');
                sidebar.innerHTML = `
                    <div class="auth-required-message">
                        <i class="bi bi-lock"></i>
                        <h5>Sign In Required</h5>
                        <p>Sign in to access your projects</p>
                        <button class="btn btn-primary btn-sm" onclick="showSignInPrompt()">
                            <i class="bi bi-box-arrow-in-right me-1"></i>
                            Sign In
                        </button>
                    </div>
                `;
                return;
            }

            const subjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
            const sidebar = document.getElementById('projectsSidebar');
            sidebar.innerHTML = `
                <h5 class="d-flex align-items-center justify-content-between">
                    <div><i class="bi bi-collection me-1"></i> Projects</div>
                    <i class="bi bi-plus-circle" style="font-size: 0.85rem; cursor: pointer;" title="Add Project" onclick="showAddProjectForm()"></i>
                </h5>
                <div class="sidebar-divider"></div>
            `;

            // Add subject projects
            subjects.forEach(subject => {
                const projectDiv = document.createElement('div');
                projectDiv.className = 'project-item';
                projectDiv.setAttribute('data-project', subject.tag);
                projectDiv.innerHTML = `
                    <div class="d-flex align-items-center justify-content-between w-100">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-book"></i>
                            <div>
                                <div class="project-title">${subject.name}</div>
                                <small class="text-muted project-subtitle">${subject.tag}</small>
                            </div>
                        </div>
                        <i class="bi bi-chevron-down expand-icon"></i>
                    </div>
                `;
                projectDiv.onclick = () => selectProject(subject.tag);
                sidebar.appendChild(projectDiv);

                // Add subsections
                const subsectionsDiv = document.createElement('div');
                subsectionsDiv.className = 'subsection';
                subsectionsDiv.setAttribute('id', `subsections-${subject.tag}`);

                SUBSECTIONS.forEach(section => {
                    const sectionDiv = document.createElement('div');
                    sectionDiv.className = 'project-item';
                    sectionDiv.setAttribute('data-project', `${subject.tag}-${section.replace(/\s+/g, '')}`);
                    sectionDiv.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="bi bi-chevron-right"></i>
                            <span>${section}</span>
                        </div>
                    `;
                    sectionDiv.onclick = (e) => {
                        e.stopPropagation();
                        // Instead of creating a new project, select the parent project and specify section
                        selectProject(subject.tag, section);
                    };
                    subsectionsDiv.appendChild(sectionDiv);
                });
                sidebar.appendChild(subsectionsDiv);
            });

            // Add Extra Curricular project
            const extraDiv = document.createElement('div');
            extraDiv.className = 'project-item mt-3';
            extraDiv.setAttribute('data-project', 'EXTRA');
            extraDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-star"></i>
                    <span>Extra Curricular</span>
                </div>
            `;
            extraDiv.onclick = () => selectProject('EXTRA');
            sidebar.appendChild(extraDiv);
        }


        //this function is also using the output from the loadprojects function so it depends mostly on local storage, my goal is only caring about the specific tasks
        // i will have to edit the part from where it is being set in local storage so that it uses versioning to
        // save and then decides weather to overwrite or append the local storage and weather to choose the
        // data from local storage or firestore
        async function selectProject(projectId, section = null) {
            // Check if user is authenticated first
            if (!window.auth || !window.auth.currentUser) {
                console.log('User not signed in');

                // Show welcome signin dialog if available
                if (typeof showWelcomeSignInDialog === 'function') {
                    showWelcomeSignInDialog();
                    return;
                } else if (typeof window.userGuidance?.showSignInPrompt === 'function') {
                    window.userGuidance.showSignInPrompt();
                    return;
                } else {
                    // Fallback to alert if no sign-in dialog is available
                    alert('Please sign in to view projects and tasks.');
                    return;
                }
            }

            // Check if this is a subsection identifier (contains hyphen)
            if (projectId.indexOf('-') !== -1 && !projectId.startsWith('EXTRA')) {
                // Extract the parent project and the section
                const parts = projectId.split('-');
                const parentProject = parts[0];
                const extractedSection = parts[1];

                // Use the parent project with the specified section
                projectId = parentProject;
                section = SUBSECTIONS.find(s => s.replace(/\s+/g, '') === extractedSection) || section;
            }

            // Update active state
            const projectElements = document.querySelectorAll('.project-item');
            projectElements.forEach(el => el.classList.remove('active'));

            // Mark the main project as active
            const selectedProject = document.querySelector(`.project-item[data-project="${projectId}"]`);
            if (selectedProject) {
                selectedProject.classList.add('active');

                // If a section is specified, also mark the section item as active
                if (section) {
                    const sectionId = `${projectId}-${section.replace(/\s+/g, '')}`;
                    const sectionItem = document.querySelector(`.project-item[data-project="${sectionId}"]`);
                    if (sectionItem) {
                        sectionItem.classList.add('active');
                        // Add parent-active class to parent project
                        selectedProject.classList.add('parent-active');
                    }
                } else {
                    // Remove the parent-active class if no section is selected
                    selectedProject.classList.remove('parent-active');
                }

                // Handle subsections expand/collapse
                if (!projectId.startsWith('EXTRA')) {
                    // Only for regular projects, not EXTRA
                    const subsectionsDiv = document.getElementById(`subsections-${projectId}`);
                    if (subsectionsDiv) {
                        // Close all other subsections first
                        document.querySelectorAll('.subsection').forEach(s => {
                            if (s.id !== `subsections-${projectId}`) {
                                s.classList.remove('expanded');
                            }
                        });

                        // Always expand the subsection when a section is specified or when clicking on a project
                        if (section || projectId.indexOf('-') === -1) {
                            subsectionsDiv.classList.add('expanded');
                        } else {
                            // Otherwise toggle it
                            subsectionsDiv.classList.toggle('expanded');
                        }
                    }
                }
            }

            // Store current project and section
            currentProject = projectId;
            currentSection = section;

            // Display tasks immediately using cached data if available
            if (window.appState.projectCache.isValid(projectId)) {
                console.log('Using cached data for project:', projectId);
                displayTasks(projectId, section);

                // Refresh data in background for next visit if needed
                if (Date.now() - window.appState.projectCache.lastFetch[projectId] > window.appState.projectCache.cacheTTL / 2) {
                    window.appState.projectCache.refreshProject(projectId).then(() => {
                        // Only update UI if user is still viewing this project
                        if (currentProject === projectId) {
                            displayTasks(projectId, section);
                        }
                    });
                }
            } else {
                // Show loading indicator for better UX
                document.getElementById('taskContainer').innerHTML = `
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading tasks...</p>
                    </div>
                `;

                // Fetch data and update display
                try {
                    await window.appState.projectCache.refreshProject(projectId);
                    displayTasks(projectId, section);
                } catch (error) {
                    console.error('Error loading project data:', error);
                    document.getElementById('taskContainer').innerHTML = `
                        <div class="alert alert-danger m-4" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Error loading tasks. Please try again.
                        </div>
                    `;
                }
            }
        }











        function formatDate(dateString) {
            const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
            return new Date(dateString).toLocaleDateString(undefined, options);
        }








        function showAddTaskForm() {
            document.getElementById('addTaskForm').style.display = 'block';
        }







        function hideAddTaskForm() {
            document.getElementById('addTaskForm').style.display = 'none';
        }











        function getPriorityColor(priority) {
            switch(priority) {
                case 'high': return 'danger';
                case 'medium': return 'warning';
                case 'low': return 'success';
                default: return 'secondary';
            }
        }











        async function addTask() {
            if (currentInputMode === 'bulk') {
                const bulkText = document.getElementById('bulkTasks').value;
                const newTasks = parseBulkTasks(bulkText);
                if (newTasks.length === 0) return;

                const tasks = await loadTasksFromFirestore(currentProject) || [];
                tasks.push(...newTasks);

                // Connect tasks to flashcard decks if possible
                try {
                    // Get the subject tag for this project
                    const projects = JSON.parse(localStorage.getItem('projects') || '[]');
                    const project = projects.find(p => p.id === currentProject);

                    if (project) {
                        const subjectTag = project.subjectTag || project.id;

                        // Process each new task
                        for (const task of newTasks) {
                            const section = task.section;

                            // Find the appropriate sub-deck for this task
                            const deckId = await window.findSubDeckForTask(subjectTag, section);

                            if (deckId) {
                                // Connect the task to the deck
                                window.connectTaskToFlashcardDeck(task.id, deckId, {
                                    title: task.title,
                                    section: task.section,
                                    projectId: currentProject,
                                    projectName: project.name
                                });

                                console.log(`Connected task ${task.id} to flashcard deck ${deckId}`);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error connecting tasks to flashcard decks:', error);
                }

                // Update both storages with new version
                const timestamp = new Date().getTime();
                localStorage.setItem(`tasks-${currentProject}`, JSON.stringify(tasks));
                localStorage.setItem(`tasks-${currentProject}-version`, timestamp.toString());
                await saveTasksToFirestore(currentProject, tasks);
            } else {
                const title = document.getElementById('taskTitle').value;
                if (!title) return;

                const description = document.getElementById('taskDescription').value;
                const dueDate = document.getElementById('taskDueDate').value;
                const priority = document.getElementById('taskPriority').value;

                const tasks = await loadTasksFromFirestore(currentProject) || [];

                // Create task with unique ID
                const taskId = Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9);
                const newTask = {
                    id: taskId,
                    title,
                    description,
                    dueDate,
                    priority,
                    section: currentSection || SUBSECTIONS[0],
                    completed: false,
                    createdAt: new Date().toISOString()
                };

                tasks.push(newTask);

                // Connect task to flashcard deck if possible
                try {
                    // Get the subject tag for this project
                    const projects = JSON.parse(localStorage.getItem('projects') || '[]');
                    const project = projects.find(p => p.id === currentProject);

                    if (project) {
                        const subjectTag = project.subjectTag || project.id;
                        const section = newTask.section;

                        // Find the appropriate sub-deck for this task
                        const deckId = await window.findSubDeckForTask(subjectTag, section);

                        if (deckId) {
                            // Connect the task to the deck
                            window.connectTaskToFlashcardDeck(taskId, deckId, {
                                title: newTask.title,
                                section: newTask.section,
                                projectId: currentProject,
                                projectName: project.name
                            });

                            console.log(`Connected task ${taskId} to flashcard deck ${deckId}`);
                        }
                    }
                } catch (error) {
                    console.error('Error connecting task to flashcard deck:', error);
                }

                // Update both storages with new version
                const timestamp = new Date().getTime();
                localStorage.setItem(`tasks-${currentProject}`, JSON.stringify(tasks));
                localStorage.setItem(`tasks-${currentProject}-version`, timestamp.toString());
                await saveTasksToFirestore(currentProject, tasks);
            }

            hideAddTaskForm();
            await displayTasks(currentProject, currentSection);
        }













        function parseBulkTasks(bulkText) {
            const lines = bulkText.trim().split('\n');
            const tasks = [];

            for (const line of lines) {
                if (!line.trim()) continue;

                let [title, dueDate, priority, description = ''] = line.split('|').map(item => item.trim());

                // If only title is provided, set default values
                if (!dueDate) {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    dueDate = tomorrow.toISOString().split('T')[0];
                }

                if (!priority) {
                    priority = 'medium';
                }

                if (title) {
                    tasks.push({
                        id: Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9),
                        title,
                        description,
                        dueDate,
                        priority: priority.toLowerCase(),
                        section: currentSection || SUBSECTIONS[0],
                        completed: false,
                        createdAt: new Date().toISOString()
                    });
                }
            }

            return tasks;
        }





        async function deleteTask(index, section) {
            if (!confirm('Are you sure you want to delete this task?')) return;

            const tasks = await loadTasksFromFirestore(currentProject) || [];
            console.log('🗂️ Loaded tasks from Firestore:', {
                totalTasks: tasks.length,
                tasks: JSON.stringify(tasks)
            });

            const sectionTasks = tasks.filter(t => t.section === section);
            console.log('📋 Tasks in section:', {
                section,
                sectionTasksCount: sectionTasks.length,
                sectionTasks: JSON.stringify(sectionTasks)
            });

            if (index >= 0 && index < sectionTasks.length) {
                const task = sectionTasks[index];
                console.log('🗑️ Selected task for deletion:', {
                    task: JSON.stringify(task),
                    index
                });

                // Normalize title for comparison
                const normalizeTitle = (title) => {
                    if (!title) return '';
                    return title.toString().toLowerCase().trim()
                        .replace(/[^\w\s]/gi, '')  // Remove special characters
                        .replace(/\s+/g, ' ');     // Normalize whitespace
                };

                // Find the task in the full tasks array by comparing normalized titles
                const taskIndex = tasks.findIndex(t =>
                    normalizeTitle(t.title) === normalizeTitle(task.title) &&
                    t.section === section
                );

                console.log('🔎 Task index in full tasks array:', {
                    taskIndex,
                    taskTitle: task.title,
                    matchedTaskTitle: taskIndex !== -1 ? tasks[taskIndex].title : 'No match'
                });

                if (taskIndex !== -1) {
                    tasks.splice(taskIndex, 1);
                    console.log('🗑️ Removed task from tasks', {
                        remainingTasksCount: tasks.length
                    });

                    // Update both storages with new version
                    const timestamp = new Date().getTime();
                    localStorage.setItem(`tasks-${currentProject}`, JSON.stringify(tasks));
                    localStorage.setItem(`tasks-${currentProject}-version`, timestamp.toString());

                    try {
                        await saveTasksToFirestore(currentProject, tasks);
                        console.log('💾 Saved updated tasks to Firestore');
                    } catch (error) {
                        console.error('❌ Error saving tasks to Firestore:', error);
                    }

                    try {
                        await displayTasks(currentProject, section);
                        console.log('📄 Displayed tasks');
                    } catch (error) {
                        console.error('❌ Error displaying tasks:', error);
                    }
                } else {
                    console.error('❌ Task index not found in full tasks array', {
                        task: JSON.stringify(task),
                        currentProject,
                        section
                    });
                }
            } else {
                console.error('❌ Task not found in section:', {
                    index,
                    section,
                    sectionTasksLength: sectionTasks.length
                });
            }
        }










        async function saveEditedTask() {
            const taskIndex = document.getElementById('editTaskIndex').value;
            const section = document.getElementById('editTaskSection').value;

            console.log('🔍 Starting saveEditedTask:', {
                taskIndex,
                section
            });

            // Normalize title for comparison
            const normalizeTitle = (title) => {
                if (!title) return '';
                return title.toString().toLowerCase().trim()
                    .replace(/[^\w\s]/gi, '')  // Remove special characters
                    .replace(/\s+/g, ' ');     // Normalize whitespace
            };

            // Retrieve tasks from Firestore
            loadTasksFromFirestore(currentProject).then(tasks => {
                console.log('🗂️ Loaded tasks from Firestore:', {
                    totalTasks: tasks.length,
                    tasks: JSON.stringify(tasks)
                });

                // Find the task in the full tasks array by comparing normalized titles
                const taskIndex = tasks.findIndex(t =>
                    normalizeTitle(t.title) === normalizeTitle(document.getElementById('editTaskTitle').value) &&
                    t.section === section
                );

                console.log('🔎 Task index in full tasks array:', {
                    taskIndex,
                    editedTitle: document.getElementById('editTaskTitle').value,
                    matchedTaskTitle: taskIndex !== -1 ? tasks[taskIndex].title : 'No match'
                });

                if (taskIndex !== -1) {
                    // Update task details
                    tasks[taskIndex] = {
                        ...tasks[taskIndex],
                        title: document.getElementById('editTaskTitle').value,
                        description: document.getElementById('editTaskDescription').value,
                        dueDate: document.getElementById('editTaskDueDate').value,
                        priority: document.getElementById('editTaskPriority').value
                    };

                    console.log('✏️ Updated task details:', {
                        updatedTask: JSON.stringify(tasks[taskIndex])
                    });

                    // Update both storages
                    const timestamp = new Date().getTime();
                    localStorage.setItem(`tasks-${currentProject}`, JSON.stringify(tasks));
                    localStorage.setItem(`tasks-${currentProject}-version`, timestamp.toString());

                    // Save to Firestore
                    saveTasksToFirestore(currentProject, tasks)
                        .then(() => {
                            console.log('💾 Saved updated tasks to Firestore');
                            displayTasks(currentProject, section);
                            closeEditTaskModal();
                        })
                        .catch(error => {
                            console.error('❌ Error saving tasks to Firestore:', error);
                        });
                } else {
                    console.error('❌ Task not found for editing', {
                        editedTitle: document.getElementById('editTaskTitle').value,
                        section
                    });
                }
            }).catch(error => {
                console.error('❌ Error loading tasks:', error);
            });
        }

        function showEditTaskModal(index, section) {
            const tasks = JSON.parse(localStorage.getItem(`tasks-${currentProject}`)) || [];
            const sectionTasks = tasks.filter(t => t.section === section);
            const task = sectionTasks[index];

            if (task) {
                document.getElementById('editTaskIndex').value = index;
                document.getElementById('editTaskSection').value = section;
                document.getElementById('editTaskTitle').value = task.title;
                document.getElementById('editTaskDescription').value = task.description || '';
                document.getElementById('editTaskDueDate').value = task.dueDate || '';
                document.getElementById('editTaskPriority').value = task.priority;
                document.getElementById('editTaskModal').style.display = 'flex';
            } else {
                console.error('Task not found in section:', { index, section });
            }
        }

        async function completeTask(index, projectId, section) {
            console.log('🔍 Starting completeTask with:', {
                index,
                projectId,
                section
            });

            const tasks = await loadTasksFromFirestore(projectId) || [];
            console.log('🗂️ Loaded tasks from Firestore:', {
                totalTasks: tasks.length,
                tasks: JSON.stringify(tasks)
            });

            const sectionTasks = tasks.filter(t => t.section === section);
            console.log('📋 Tasks in section:', {
                section,
                sectionTasksCount: sectionTasks.length,
                sectionTasks: JSON.stringify(sectionTasks)
            });

            if (index >= 0 && index < sectionTasks.length) {
                const task = sectionTasks[index];
                console.log('✅ Selected task:', {
                    task: JSON.stringify(task),
                    index
                });

                // Normalize title for comparison
                const normalizeTitle = (title) => {
                    if (!title) return '';
                    return title.toString().toLowerCase().trim()
                        .replace(/[^\w\s]/gi, '')  // Remove special characters
                        .replace(/\s+/g, ' ');     // Normalize whitespace
                };

                // Find the task in the full tasks array by comparing normalized titles
                const taskIndex = tasks.findIndex(t =>
                    normalizeTitle(t.title) === normalizeTitle(task.title) &&
                    t.section === section
                );

                console.log('🔎 Task index in full tasks array:', {
                    taskIndex,
                    taskTitle: task.title,
                    matchedTaskTitle: taskIndex !== -1 ? tasks[taskIndex].title : 'No match'
                });

                if (taskIndex !== -1) {
                    // Create completed task object
                    const completedTask = {
                        ...task,
                        completedAt: new Date().toISOString()
                    };

                    console.log('🏁 Completed task object:', {
                        completedTask: JSON.stringify(completedTask)
                    });

                    // Save completed task to both storages
                    try {
                        await saveCompletedTaskToFirestore(projectId, completedTask);
                        console.log('💾 Saved completed task to Firestore');
                    } catch (error) {
                        console.error('❌ Error saving completed task to Firestore:', error);
                    }

                    // Remove from active tasks
                    tasks.splice(taskIndex, 1);
                    console.log('🗑️ Removed task from active tasks', {
                        remainingTasksCount: tasks.length
                    });

                    // Update active tasks in both storages with new version
                    const timestamp = new Date().getTime();
                    localStorage.setItem(`tasks-${projectId}`, JSON.stringify(tasks));
                    localStorage.setItem(`tasks-${projectId}-version`, timestamp.toString());

                    try {
                        await saveTasksToFirestore(projectId, tasks);
                        console.log('💾 Saved updated tasks to Firestore');
                    } catch (error) {
                        console.error('❌ Error saving tasks to Firestore:', error);
                    }

                    // Play completion sound
                    playPopSound();
                    console.log('🔊 Played completion sound');

                    try {
                        await displayTasks(projectId, section);
                        console.log('📄 Displayed tasks');
                    } catch (error) {
                        console.error('❌ Error displaying tasks:', error);
                    }
                } else {
                    console.error('❌ Task index not found in full tasks array', {
                        task: JSON.stringify(task),
                        projectId,
                        section
                    });
                }
            } else {
                console.error('❌ Task not found in section:', {
                    index,
                    section,
                    sectionTasksLength: sectionTasks.length
                });
            }
        }







        function toggleInputMode(mode) {
            currentInputMode = mode;
            document.getElementById('singleTaskInput').style.display = mode === 'single' ? 'block' : 'none';
            document.getElementById('bulkTaskInput').style.display = mode === 'bulk' ? 'block' : 'none';
        }




























        function closeEditTaskModal() {
            document.getElementById('editTaskModal').style.display = 'none';
        }







        // Close modal when clicking outside
        document.getElementById('editTaskModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeEditTaskModal();
            }
        });

        async function showWeightageModal(projectId) {
            const modal = document.getElementById('weightageModal');
            const inputsContainer = document.getElementById('weightageInputs');

            // Store the project ID directly in the modal's dataset
            modal.dataset.projectId = projectId;

            // Get project name for title (if available)
            const projectElement = document.querySelector(`.project-card[data-id="${projectId}"]`);
            if (projectElement) {
                const titleElement = projectElement.querySelector('.project-title');
                if (titleElement) {
                    const h5Element = modal.querySelector('h5');
                    if (h5Element) {
                        h5Element.textContent = `Set Weightages for ${titleElement.textContent}`;
                    }
                }
            }

            // Attempt to get weightages from both systems
            const projectWeightages = JSON.parse(localStorage.getItem('projectWeightages') || '{}');
            const subjectWeightagesJson = localStorage.getItem('subjectWeightages') || '{}';
            const subjectWeightages = JSON.parse(subjectWeightagesJson);

            // Prepare section weightages object, first with defaults
            let sectionWeightages = {};
            SUBSECTIONS.forEach(section => {
                sectionWeightages[section] = { min: 0, max: 0 };
            });

            // Try to get weightages from subject marks system if available
            if (subjectWeightages[projectId]) {
                // Map categories to sections
                const categoryToSection = {
                    assignment: 'Assignment',
                    quiz: 'Quizzes',
                    midterm: 'Mid Term / OHT',
                    final: 'Finals',
                    revision: 'Revision'
                };

                // Convert subject weightages to section format
                for (const category in subjectWeightages[projectId]) {
                    const sectionName = categoryToSection[category] || category;
                    const weight = subjectWeightages[projectId][category];

                    if (SUBSECTIONS.includes(sectionName)) {
                        sectionWeightages[sectionName] = {
                            min: Math.max(0, weight - 5),
                            max: Math.min(100, weight + 5)
                        };
                    }
                }
            }

            // If project weightages exist, they take precedence
            if (projectWeightages[projectId]) {
                sectionWeightages = {...sectionWeightages, ...projectWeightages[projectId]};
            }

            // Generate inputs for each subsection
            inputsContainer.innerHTML = SUBSECTIONS.map(section => {
                const sectionData = sectionWeightages[section] || { min: 0, max: 0 };
                return `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label>${section}</label>
                            <span class="weightage-avg" data-section="${section}">
                                ${((sectionData.min + sectionData.max) / 2).toFixed(1)}%
                            </span>
                        </div>
                        <div class="weightage-input-group" data-section="${section}">
                            <input type="number"
                                   class="weightage-input"
                                   data-section="${section}"
                                   data-type="min"
                                   value="${sectionData.min}"
                                   min="0"
                                   max="100"
                                   oninput="updateWeightageAverage()">
                            <span>to</span>
                            <input type="number"
                                   class="weightage-input"
                                   data-section="${section}"
                                   data-type="max"
                                   value="${sectionData.max}"
                                   min="0"
                                   max="100"
                                   oninput="updateWeightageAverage()">
                        </div>
                    </div>
                `;
            }).join('');

            // Update total average
            updateWeightageAverage();

            // Show the modal
            modal.style.display = 'block';
        }

        function updateWeightageAverage() {
            const sections = {};
            let totalAverage = 0;

            // Calculate average for each section
            SUBSECTIONS.forEach(section => {
                const minInput = document.querySelector(`input[data-section="${section}"][data-type="min"]`);
                const maxInput = document.querySelector(`input[data-section="${section}"][data-type="max"]`);
                const avgSpan = document.querySelector(`span.weightage-avg[data-section="${section}"]`);

                const min = parseInt(minInput.value) || 0;
                const max = parseInt(maxInput.value) || 0;
                const avg = (min + max) / 2;

                sections[section] = avg;
                totalAverage += avg;

                if (avgSpan) {
                    avgSpan.textContent = `${avg.toFixed(1)}%`;
                }
            });

            // Update total average display
            const totalAverageSpan = document.getElementById('totalAverage');
            totalAverageSpan.textContent = `${totalAverage.toFixed(1)}%`;
            totalAverageSpan.style.color = Math.abs(totalAverage - 100) < 0.1 ? 'var(--secondary-color)' : 'var(--primary-color)';
        }

        async function saveWeightages() {
            const modal = document.getElementById('weightageModal');
            const projectId = modal.dataset.projectId;
            const weightages = {};

            // Get all sections
            const allSections = Array.from(document.querySelectorAll('.weightage-input-group')).map(group => group.dataset.section);

            // Collect all weightages
            allSections.forEach(section => {
                const minInput = document.querySelector(`input[data-section="${section}"][data-type="min"]`);
                const maxInput = document.querySelector(`input[data-section="${section}"][data-type="max"]`);

                const min = parseInt(minInput.value) || 0;
                const max = parseInt(maxInput.value) || 0;
                const avg = Math.round((min + max) / 2);

                weightages[section] = { min, max, avg };
            });

            // Validate total average is close to 100%
            const totalAverage = allSections.reduce((sum, section) => sum + weightages[section].avg, 0);
            if (Math.abs(totalAverage - 100) > 5) {
                alert('The average of all weightages must sum to 100%');
                return;
            }

            // Save weightages for this project
            const allWeightages = JSON.parse(localStorage.getItem('projectWeightages') || '{}');
            allWeightages[projectId] = weightages;
            localStorage.setItem('projectWeightages', JSON.stringify(allWeightages));
            await saveWeightagesToFirestore(allWeightages);

            // Sync with subject marks system
            if (window.syncProjectToSubjectWeightages) {
                window.syncProjectToSubjectWeightages(projectId, weightages);
            }

            closeWeightageModal();
            await displayTasks(projectId, currentSection);
        }

        function closeWeightageModal() {
            const modal = document.getElementById('weightageModal');
            modal.style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('weightageModal');
            if (event.target === modal) {
                closeWeightageModal();
            }
        }

        async function showHistoryModal(projectId) {
            const completedTasks = JSON.parse(localStorage.getItem(`completed-tasks-${projectId}`) || '[]');
            const historyList = document.getElementById('historyList');

            if (completedTasks.length === 0) {
                historyList.innerHTML = `
                    <div class="no-history">
                        <i class="bi bi-clock-history mb-2" style="font-size: 2rem;"></i>
                        <p>No completed tasks yet</p>
                    </div>
                `;
            } else {
                historyList.innerHTML = completedTasks.map(task => `
                    <div class="completed-task">
                        <div class="task-details">
                            <h6 class="mb-1">${task.title}</h6>
                            <p class="mb-1 text-secondary">${task.description || 'No description'}</p>
                            <div class="d-flex align-items-center gap-3">
                                <span class="badge bg-secondary">${task.section}</span>
                                <span class="priority-badge" style="background-color: ${getPriorityColor(task.priority)}">
                                    ${task.priority}
                                </span>
                                <span class="due-date">
                                    <i class="bi bi-calendar-event"></i>
                                    ${formatDate(task.dueDate)}
                                </span>
                            </div>
                        </div>
                        <div class="completion-date">
                            Completed ${formatDate(task.completedAt)}
                        </div>
                    </div>
                `).join('');
            }

            document.getElementById('historyModal').style.display = 'flex';
        }

        function closeHistoryModal() {
            document.getElementById('historyModal').style.display = 'none';
        }

        // Close history modal when clicking outside
        document.getElementById('historyModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeHistoryModal();
            }
        });

        function getSectionIcon(section) {
            const icons = {
                'Revision': 'book',
                'Assignment': 'file-text',
                'Quizzes': 'question-circle',
                'Mid Term / OHT': 'clipboard-data',
                'Finals': 'trophy'
            };
            return icons[section] || 'folder';
        }

        // Initialize the page
        window.onload = function() {
            loadProjects();
            // Add fade-in animation to body after load
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.body.style.transition = 'opacity 0.3s ease';
            }, 0);

            // Add auth state change listener
            if (window.auth) {
                window.auth.onAuthStateChanged(user => {
                    if (user) {
                        console.log('User is signed in, refreshing projects');
                        loadProjects();
                    } else {
                        console.log('User is signed out');
                    }
                });
            }
        };

        /**
         * Show the sign-in prompt using the appropriate module
         */
        function showSignInPrompt() {
            if (typeof showWelcomeSignInDialog === 'function') {
                showWelcomeSignInDialog();
            } else if (typeof window.userGuidance?.showSignInPrompt === 'function') {
                window.userGuidance.showSignInPrompt();
            } else if (typeof window.signInWithGoogle === 'function') {
                window.signInWithGoogle();
            } else {
                alert('Sign-in functionality is not available');
            }
        }

        /**
         * Upload a file attachment directly to the selected task
         * @param {string} taskId - The unique ID of the task
         * @param {string} projectId - The project ID
         * @param {string} section - The section name
         */
        function uploadAttachment(taskId, projectId, section) {
            console.log(`Upload attachment called for task: ${taskId}, project: ${projectId}, section: ${section}`);

            // Create a file input element
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);

            // Listen for file selection
            fileInput.addEventListener('change', async (event) => {
                if (event.target.files && event.target.files.length > 0) {
                    const files = Array.from(event.target.files);

                    // Get task data from DOM attributes
                    const container = document.getElementById(`task-attachments-${taskId}`);
                    if (!container) {
                        alert('Error: Cannot find task container');
                        return;
                    }

                    // Get task details from data attributes
                    const actualTaskId = container.dataset.taskId || taskId;
                    const taskTitle = container.dataset.taskTitle;
                    const taskSection = container.dataset.taskSection || section;
                    const actualProjectId = container.dataset.projectId || projectId;

                    console.log(`Processing files for task: ${actualTaskId}, project: ${actualProjectId}, section: ${taskSection}`);

                    // Show upload indicator
                    const uploadStatus = document.createElement('div');
                    uploadStatus.className = 'upload-status';
                    uploadStatus.innerHTML = `<i class="bi bi-arrow-repeat"></i> Uploading ${files.length} file(s)...`;
                    container.appendChild(uploadStatus);

                    try {
                        // Upload each file using Google Drive API
                        for (const file of files) {
                            // Use the correct googleDriveAPI reference
                            if (window.googleDriveAPI && typeof window.googleDriveAPI.uploadFile === 'function') {
                                await window.googleDriveAPI.uploadFile(file, actualTaskId);
                            } else if (typeof googleDriveAPI !== 'undefined' && typeof googleDriveAPI.uploadFile === 'function') {
                                await googleDriveAPI.uploadFile(file, actualTaskId);
                            } else {
                                throw new Error('Google Drive API not available');
                            }
                        }

                        // Show success message
                        uploadStatus.innerHTML = `<i class="bi bi-check-circle"></i> Upload complete!`;
                        uploadStatus.style.color = 'var(--secondary-color)';

                        // Refresh attachments after upload
                        if (window.taskAttachments && typeof window.taskAttachments.refreshTask === 'function') {
                            window.taskAttachments.refreshTask(actualTaskId);
                        } else if (typeof taskAttachments !== 'undefined' && typeof taskAttachments.init === 'function') {
                            setTimeout(() => {
                                if (container) {
                                    taskAttachments.init(actualTaskId, container);
                                }
                                // Remove upload status after refresh
                                setTimeout(() => {
                                    uploadStatus.remove();
                                }, 2000);
                            }, 1000);
                        } else {
                            console.warn('Task attachments module not available for refresh');
                            setTimeout(() => {
                                uploadStatus.remove();
                            }, 5000);
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        uploadStatus.innerHTML = `<i class="bi bi-exclamation-circle"></i> Upload failed: ${error.message}`;
                        uploadStatus.style.color = 'var(--primary-color)';

                        // Remove error message after a delay
                        setTimeout(() => {
                            uploadStatus.remove();
                        }, 5000);
                    }
                }

                // Clean up file input
                document.body.removeChild(fileInput);
            });

            // Trigger file selection dialog
            fileInput.click();
        }

        //okay this is the real deal that i need to sync with firestore, but these are getting the item from the local storage of academic-details.html, so this
        //function is going to be called from there no other change is needed
        async function displayTasks(projectId, section = null) {
            const container = document.getElementById('taskContainer');

            // Check authentication status
            if (!window.auth || !window.auth.currentUser) {
                console.log('User not signed in when trying to display tasks');
                container.innerHTML = `
                    <div class="auth-required-message">
                        <i class="bi bi-lock"></i>
                        <h5>Sign In Required</h5>
                        <p class="text-secondary">Please sign in to view your tasks.</p>
                        <button class="btn btn-primary" onclick="showSignInPrompt()">
                            <i class="bi bi-box-arrow-in-right"></i>
                            Sign In
                        </button>
                    </div>
                `;
                return;
            }

            // Use cached data if available, otherwise load from Firestore
            let tasks;
            let weightages;

            if (window.appState.projectCache.isValid(projectId)) {
                tasks = window.appState.projectCache.tasks[projectId] || [];
                weightages = window.appState.projectCache.weightages || {};
            } else {
                // If we're here, we should be showing a loading indicator already
                if (!window.appState.projectCache.isFetching[projectId]) {
                    try {
                        await window.appState.projectCache.refreshProject(projectId);
                        tasks = window.appState.projectCache.tasks[projectId] || [];
                        weightages = window.appState.projectCache.weightages || {};
                    } catch (error) {
                        console.error('Error loading tasks in displayTasks:', error);
                        container.innerHTML = `
                            <div class="alert alert-danger m-4" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Error loading tasks. Please try again.
                            </div>
                        `;
                        return;
                    }
                } else {
                    // Already fetching, show loading indicator and return
                    return;
                }
            }

            console.log('Displaying tasks from cache for:', projectId);

            container.innerHTML = `
                <div class="project-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="project-title">
                            <i class="bi bi-collection"></i>
                            ${projectId}
                            ${section ? `<span class="badge bg-secondary">${section}</span>` : ''}
                        </div>
                        <div class="d-flex gap-3">
                            <button class="history-btn" onclick="showHistoryModal('${projectId}')">
                                <i class="bi bi-clock-history"></i>
                                History
                        </div>
                    </div>
                <div class="section-nav">
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn ${!section ? 'btn-primary' : 'btn-outline-secondary'}"
                                onclick="selectProject('${projectId}')">
                            <i class="bi bi-grid"></i>
                            All Sections
                        </button>
                        ${SUBSECTIONS.map(s => `
                            <button class="btn ${section === s ? 'btn-primary' : 'btn-outline-secondary'}"
                                    onclick="selectProject('${projectId}', '${s}')">
                                <i class="bi bi-${getSectionIcon(s)}"></i>
                                ${s}
                            </button>
                        `).join('')}
                    </div>
                </div>
                <button class="add-task-btn mb-4" onclick="showAddTaskForm()">
                    <i class="bi bi-plus-circle me-2"></i> Add new task
                </button>
                <div id="addTaskForm" class="task-form mb-4">
                    <div class="task-input-toggle mb-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleInputMode('single')">Single Task</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleInputMode('bulk')">Bulk Add</button>
                    </div>

                    <div id="singleTaskInput">
                        <input type="text" id="taskTitle" class="form-control mb-2" placeholder="Task Title">
                        <textarea id="taskDescription" class="form-control mb-2" placeholder="Description (optional)"></textarea>
                        <input type="date" id="taskDueDate" class="form-control mb-2">
                        <select id="taskPriority" class="form-control mb-3">
                            <option value="low">Low Priority</option>
                            <option value="medium">Medium Priority</option>
                            <option value="high">High Priority</option>
                        </select>
                    </div>

                    <div id="bulkTaskInput" style="display: none;">
                        <div class="input-format-example">
                            Format: Title | Due Date (YYYY-MM-DD) | Priority (low/medium/high) | Description<br>
                            Example: Complete math homework | 2024-01-20 | high | Chapter 5 exercises
                        </div>
                        <textarea id="bulkTasks" class="bulk-input" placeholder="Enter one task per line..."></textarea>
                    </div>

                    <button onclick="addTask()" class="btn btn-primary">Add Task(s)</button>
                    <button onclick="hideAddTaskForm()" class="btn btn-secondary">Cancel</button>
                </div>
                <div id="taskList" class="fade-in">
                    ${SUBSECTIONS.filter(s => !section || s === section).map(s => {
                        const sectionWeightage = weightages[s] || { min: 0, max: 0, avg: 0 };
                        const sectionTasks = tasks.filter(task => task.section === s);

                        return `
                            <div class="mb-4">
                                <div class="section-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="section-title">
                                            <i class="bi bi-${getSectionIcon(s)}"></i>
                                            ${s}
                                        </div>
                                        <span class="weightage-badge">
                                            ${sectionWeightage.min}-${sectionWeightage.max}% (avg: ${((sectionWeightage.min + sectionWeightage.max) / 2).toFixed(1)}%)
                                        </span>
                                    </div>
                                </div>
                                ${sectionTasks.length === 0 ? `
                                    <div class="empty-section">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="bi bi-inbox"></i>
                                            <div>No tasks in this section</div>
                                        </div>
                                    </div>
                                ` : sectionTasks.map((task, index) => `
                                    <div class="task-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="d-flex align-items-start">
                                                <div class="completion-circle"
                                                     onclick="completeTask(${index}, '${projectId}', '${s}')"
                                                     title="Mark as complete"></div>
                                                <div>
                                                    <h5 class="mb-2">${task.title}</h5>
                                                    <p class="mb-2 text-secondary">${task.description || 'No description'}</p>
                                                    <div class="d-flex align-items-center gap-3">
                                                        <span class="due-date">
                                                            <i class="bi bi-calendar-event"></i>
                                                            ${formatDate(task.dueDate)}
                                                        </span>
                                                        <span class="priority-badge" style="background-color: ${getPriorityColor(task.priority)}">
                                                            ${task.priority}
                                                        </span>
                                                    </div>
                                                    <div class="task-attachments-container mt-3"
                                                         id="task-attachments-${task.id || index + '-' + s.replace(/\s+/g, '')}"
                                                         data-task-id="${task.id || ''}"
                                                         data-task-title="${task.title || ''}"
                                                         data-task-section="${s || ''}"
                                                         data-project-id="${projectId || ''}">
                                                         <button class="btn btn-sm btn-outline-secondary upload-btn"
                                                                onclick="uploadAttachment('${task.id || index + '-' + s.replace(/\s+/g, '')}', '${projectId}', '${s}')">
                                                             <i class="bi bi-paperclip"></i> Attach file
                                                         </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="task-actions">
                                                <button class="btn btn-sm btn-outline-primary" onclick="showEditTaskModal(${index}, '${s}')">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteTask(${index}, '${s}')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                    }).join('')}
                </div>
            `;

            // Initialize task attachments for each task
            setTimeout(() => {
                tasks.forEach((task, index) => {
                    SUBSECTIONS.forEach(s => {
                        if (!section || section === s) {
                            if (task.section === s) {
                                // Make sure we're using the actual task ID consistently
                                const taskId = task.id || `${index}-${s.replace(/\s+/g, '')}`;
                                const container = document.getElementById(`task-attachments-${taskId}`);

                                if (container) {
                                    console.log(`Initializing attachments for task: ${taskId}, title: ${task.title}`);

                                    // Make sure we store all necessary task data in the container
                                    container.dataset.taskId = taskId;
                                    container.dataset.taskTitle = task.title || '';
                                    container.dataset.taskSection = s || '';
                                    container.dataset.projectId = projectId || '';

                                    // Initialize task attachments
                                    if (window.taskAttachments && typeof window.taskAttachments.init === 'function') {
                                        window.taskAttachments.init(taskId, container);
                                    } else if (typeof taskAttachments !== 'undefined' && typeof taskAttachments.init === 'function') {
                                        taskAttachments.init(taskId, container);
                                    } else {
                                        console.warn('Task attachments module not available');
                                    }
                                }
                            }
                        }
                    });
                });

                // Register global event listeners for attachment operations
                if (!window._attachmentListenersRegistered) {
                    window._attachmentListenersRegistered = true;

                    // Listen for attachment updates from Google Drive API
                    window.addEventListener('file-upload-success', (event) => {
                        const { file, task } = event.detail;
                        if (file && task && task.id) {
                            console.log(`File upload success event for task: ${task.id}`);
                            // Refresh attachments for this task
                            const container = document.getElementById(`task-attachments-${task.id}`);
                            if (container) {
                                if (window.taskAttachments && typeof window.taskAttachments.init === 'function') {
                                    window.taskAttachments.init(task.id, container);
                                } else if (typeof taskAttachments !== 'undefined' && typeof taskAttachments.init === 'function') {
                                    taskAttachments.init(task.id, container);
                                }
                            }
                        }
                    });

                    // Add drag-over event listeners to task containers
                    document.querySelectorAll('.task-item').forEach(taskItem => {
                        taskItem.addEventListener('dragover', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            taskItem.classList.add('drag-highlight');
                        });

                        taskItem.addEventListener('dragleave', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            taskItem.classList.remove('drag-highlight');
                        });
                    });
                }
            }, 100);
        }

        async function saveTasksToFirestore(projectId, tasks) {
            // Invalidate cache for this project when tasks are modified
            window.appState.projectCache.clearProject(projectId);

            // Original saveTasksToFirestore implementation
            try {
                if (!window.auth.currentUser) {
                    console.log('User not signed in, cannot save tasks');
                    return;
                }

                const db = firebase.firestore();
                const userDoc = db.collection('users').doc(window.auth.currentUser.uid);
                const batch = db.batch();

                batch.set(userDoc.collection('projects').doc(projectId), {
                    tasks: tasks,
                    lastModified: firebase.firestore.FieldValue.serverTimestamp()
                });

                await batch.commit();
                console.log('Tasks saved to Firestore');

                // After successful save, update cache
                window.appState.projectCache.setTasks(projectId, tasks);

                // Trigger cross-tab sync
                if (window.crossTabSync) {
                    window.crossTabSync.notifyUserAction('task-update', {
                        projectId,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.error('Error saving tasks to Firestore:', error);
                throw error;
            }
        }
    </script>
    <script>
        // Add this at the end of the file
        document.addEventListener('DOMContentLoaded', function() {
            // Setup sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('projectsSidebar');
            const mainContent = document.querySelector('.main-content');

            if (sidebarToggle && sidebar && mainContent) {
                sidebarToggle.addEventListener('click', function() {
                    const isCollapsed = sidebar.classList.contains('closing');

                    if (isCollapsed) {
                        // Expanding
                        sidebar.classList.remove('closing');
                        mainContent.classList.remove('full-width');
                        sidebarToggle.classList.remove('collapsed');
                        sidebarToggle.querySelector('i').className = 'bi bi-chevron-left';
                    } else {
                        // Collapsing
                        sidebar.classList.add('closing');
                        mainContent.classList.add('full-width');
                        sidebarToggle.classList.add('collapsed');
                        sidebarToggle.querySelector('i').className = 'bi bi-chevron-right';
                    }
                });
            }

            // Re-run loadProjects to ensure the subsection styling is applied
            if (typeof loadProjects === 'function') {
                loadProjects();
            }
        });
    </script>
    <script src="../js/inject-header.js"></script>
</body>
</html>